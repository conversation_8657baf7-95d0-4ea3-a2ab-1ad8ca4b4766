{"version": "3.7.6", "private": true, "name": "react-todolist", "description": "To-do list made with React.", "author": "<EMAIL>", "license": "ISC", "main": "index.js", "scripts": {"install:global": "node ./bin/preinstall-global-dependencies.js", "install:clean": "rm -rf node_modules package-lock.json", "preinstall": "npm run install:global", "postinstall": "npm run lint", "start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "prettier:fix": "prettier --write \"./**/*.{css,scss,sass,json,js,jsx,ts,tsx}\"", "eslint:fix": "eslint \"**/*.{json,js,jsx,ts,tsx}\" --fix --ignore-path .eslintignore", "stylelint:fix": "stylelint \"**/*.{css,scss,sass}\" --fix --ignore-path .stylelintignore", "lint": "npm run prettier:fix && npm run eslint:fix && npm run stylelint:fix", "changelog:init": "conventional-changelog -p angular -i CHANGELOG.md -s -r 0", "changelog:update": "standard-version --tag-prefix='' --release-commit-message-format 'ci(changelog): update files with the new version {{currentTag}}'", "changelog:update:gitflow": "sh ./bin/standar-version-updater-gitflow.sh", "deploy": "node gh-pages-deploy.mjs", "prepare": "husky"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@reduxjs/toolkit": "^2.3.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-redux": "^9.1.2", "styled-components": "^6.1.13"}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/eslint-parser": "^7.27.5", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.14", "@types/node": "^22.10.2", "@types/react": "^18.3.17", "@types/react-dom": "^18.3.5", "commitizen": "^4.3.1", "conventional-changelog-cli": "^5.0.0", "cz-conventional-changelog": "^3.3.0", "eslint": "^8.57.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-jsonc": "^2.20.1", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "husky": "^9.1.7", "lint-staged": "^16.1.2", "prettier": "^3.6.1", "react-scripts": "5.0.1", "sass": "^1.89.2", "standard-version": "^9.5.0", "stylelint": "^16.21.0", "stylelint-config-property-sort-order-smacss": "^10.0.0", "stylelint-config-sass-guidelines": "^12.1.0", "stylelint-config-standard": "^38.0.0", "stylelint-scss": "^6.12.1", "typescript": "^4.9.5", "web-vitals": "^4.2.4"}, "globalDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "commitizen": "^4.3.1", "conventional-changelog-cli": "^5.0.0", "cz-conventional-changelog": "^3.3.0", "prettier": "^3.6.1", "standard-version": "^9.5.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "engines": {"node": "20.18.0", "npm": "10.8.2"}}