import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTrash } from '@fortawesome/free-solid-svg-icons';
import { 
  selectTotalTaskList, 
  selectTotalTaskListDone,
  removeTaskList,
  removeTaskListDone 
} from '../../store/slices/tasksSlice';
import UIButton from '../UI/UIButton';
import UITag from '../UI/UITag';
import './DashboardInfo.scss';

const DashboardInfo = () => {
  const dispatch = useDispatch();
  const totalTaskList = useSelector(selectTotalTaskList);
  const totalTaskListDone = useSelector(selectTotalTaskListDone);

  const handleRemoveTaskList = () => {
    dispatch(removeTaskList());
  };

  const handleRemoveTaskListDone = () => {
    dispatch(removeTaskListDone());
  };

  return (
    <div className="dashboard-info">
      <ul className="dashboard-info__list">
        <li className="dashboard-info__item">
          <UITag
            tagName="Tasks"
            tagValue={totalTaskList}
          />
        </li>
        <li className="dashboard-info__item">
          <UITag
            tagName="Tasks Done"
            tagValue={totalTaskListDone}
          />
        </li>
      </ul>
      <ul className="dashboard-info__list">
        {totalTaskListDone !== 0 && (
          <li className="dashboard-info__item">
            <UIButton
              className="button--bg-color-error button--small"
              onClick={handleRemoveTaskListDone}
            >
              <span className="button__icon">
                <i className="icon">
                  <FontAwesomeIcon icon={faTrash} />
                </i>
              </span>
              <span className="button__text">
                tasks done
              </span>
            </UIButton>
          </li>
        )}
        <li className="dashboard-info__item">
          <UIButton
            className="button--bg-color-error button--small"
            onClick={handleRemoveTaskList}
          >
            <span className="button__icon">
              <i className="icon">
                <FontAwesomeIcon icon={faTrash} />
              </i>
            </span>
            <span className="button__text">
              tasks
            </span>
          </UIButton>
        </li>
      </ul>
    </div>
  );
};

export default DashboardInfo;
