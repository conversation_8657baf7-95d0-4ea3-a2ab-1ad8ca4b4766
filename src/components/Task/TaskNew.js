import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { useSelector, useDispatch } from 'react-redux';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPlusCircle } from '@fortawesome/free-solid-svg-icons';
import { addTask, selectTaskLast } from '../../store/slices/tasksSlice';
import UIButton from '../UI/UIButton';
import './TaskNew.scss';

const TaskNew = ({ onAddTask }) => {
  const dispatch = useDispatch();
  const taskLast = useSelector(selectTaskLast);
  const [taskName, setTaskName] = useState('');

  const createTaskId = () => {
    if (taskLast) {
      return taskLast.id + 1;
    }
    return 1;
  };

  const createTaskNew = () => {
    const trimmedName = taskName.trim();
    
    if (trimmedName !== '') {
      const task = {
        id: createTaskId(),
        name: trimmedName,
      };
      
      dispatch(addTask(task));
      setTaskName('');
      
      if (onAddTask) {
        onAddTask();
      }
    }
  };

  const handleInputChange = (e) => {
    setTaskName(e.target.value);
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      createTaskNew();
    }
  };

  return (
    <div className="task-new">
      <div className="task-new__field">
        <input
          value={taskName}
          onChange={handleInputChange}
          onKeyPress={handleKeyPress}
          className="task-new__input"
          type="text"
          placeholder="New task"
          autoFocus
        />
        <UIButton
          className="task-new__button-add button--icon"
          onClick={createTaskNew}
        >
          <span className="button__icon">
            <i className="icon">
              <FontAwesomeIcon icon={faPlusCircle} />
            </i>
          </span>
        </UIButton>
      </div>
    </div>
  );
};

TaskNew.propTypes = {
  onAddTask: PropTypes.func,
};

export default TaskNew;
