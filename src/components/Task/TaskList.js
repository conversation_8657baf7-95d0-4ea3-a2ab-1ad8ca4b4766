import React, { useEffect, useRef } from 'react';
import { useSelector } from 'react-redux';
import { selectTaskList, selectTotalTaskList } from '../../store/slices/tasksSlice';
import TaskPreview from './TaskPreview';
import UIIcon from '../UI/UIIcon';
import './TaskList.scss';

const TaskList = () => {
  const taskList = useSelector(selectTaskList);
  const totalTaskList = useSelector(selectTotalTaskList);
  const taskListWrapperRef = useRef(null);

  const scrollToBottom = () => {
    if (taskListWrapperRef.current) {
      const element = taskListWrapperRef.current;
      element.scrollTop = element.scrollHeight;
    }
  };

  useEffect(() => {
    scrollToBottom();
  }, [taskList]);

  return (
    <div
      ref={taskListWrapperRef}
      className="task-list__wrapper"
    >
      {totalTaskList !== 0 ? (
        <ul className="task-list">
          {taskList.map((item) => (
            <li
              key={item.id}
              className="task-list__item"
            >
              <TaskPreview taskItem={item} />
            </li>
          ))}
        </ul>
      ) : (
        <div className="task-list__message">
          <p>Your task list is empty</p>
          <UIIcon name="emptyTasks" />
        </div>
      )}
    </div>
  );
};

export default TaskList;
