import React from 'react';
import PropTypes from 'prop-types';
import './UITag.scss';

const UITag = ({ tagName, tagValue }) => {
  const isValueNumber = typeof tagValue === 'number';

  return (
    <div className={`tag ${isValueNumber ? 'tag--value-number' : ''}`}>
      <span className="tag__name">
        {tagName}
      </span>
      <span className="tag__value">
        {tagValue}
      </span>
    </div>
  );
};

UITag.propTypes = {
  tagName: PropTypes.string.isRequired,
  tagValue: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number,
  ]),
};

export default UITag;
