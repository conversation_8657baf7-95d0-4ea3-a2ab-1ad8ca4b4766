// BASE
// GLOBAL
// =================================================
@use "sass:map";
@use "../abstracts/mixins" as *;
@use "../abstracts/variables" as *;

*,
*::before,
*::after {
	margin: 0;
	padding: 0;
	box-sizing: border-box;
}

*::selection {
	color: $color-white;
	background-color: $color-brand-1;
}

html,
body,
textarea,
select,
input,
button {
	font-family: $font-brand-2;
	line-height: 100%;
}

html {
	width: 100%;
	height: 100%;
	overflow-x: hidden;
	font-size: 10px;
	font-weight: 400;
	-ms-text-size-adjust: 100%;
	-webkit-text-size-adjust: 100%;
	-moz-osx-font-smoothing: grayscale;
	-webkit-font-smoothing: antialiased;

	@include media("sm", "max", "height") {
		height: inherit;
	}
}

body {
	padding: 5rem 3rem;
	height: 100%;
	min-height: 100vh;
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-direction: column;
	font-size: 1rem;
	overflow-x: hidden;
	transition: all 0.5s ease-in-out 0s;

	@include media("sm") {
		padding: 4rem 2.5rem;
		height: inherit;
		justify-content: flex-start;
	}

	@include media("md", "max", "height") {
		padding: 4rem 2.5rem;
		height: inherit;
		justify-content: flex-start;
	}

	@media screen and (min-height: #{map.get($breakpoints, 'md')}) {
		padding: 6rem 3rem;
	}

	&:after {
		content: "";
		width: 100%;
		height: 100%;
		display: inline-block;
		position: absolute;
		top: 0;
		left: 0;
		z-index: -1;
		background: linear-gradient(to bottom, $color-brand-1 0%, $color-brand-2 100%);
	}
}

a {
	display: inline-block;
	text-decoration: none;
	color: inherit;

	&:hover {
		color: inherit;
	}
}

p {
	&:not(:last-child) {
		margin-bottom: 1rem;
	}

	&:only-of-type {
		margin-bottom: 0;
	}
}

.app {
	width: 100%;
}


