import { createAsyncThunk } from '@reduxjs/toolkit';
import { updateTaskListFromLocalStorage } from '../slices/tasksSlice';

export const checkTaskListLocalStorage = createAsyncThunk(
  'tasks/checkTaskListLocalStorage',
  async (_, { dispatch, getState }) => {
    if (localStorage.getItem('tasks')) {
      try {
        const tasks = JSON.parse(localStorage.getItem('tasks'));
        dispatch(updateTaskListFromLocalStorage(tasks));
      } catch (e) {
        localStorage.removeItem('tasks');
      }
    } else {
      const { tasks } = getState();
      localStorage.setItem('tasks', JSON.stringify(tasks.list));
    }
  }
);

export const createTaskListLocalStorage = createAsyncThunk(
  'tasks/createTaskListLocalStorage',
  async (_, { getState }) => {
    const { tasks } = getState();
    localStorage.setItem('tasks', JSON.stringify(tasks.list));
  }
);

export const removeTaskListLocalStorage = createAsyncThunk(
  'tasks/removeTaskListLocalStorage',
  async () => {
    localStorage.removeItem('tasks');
  }
);
