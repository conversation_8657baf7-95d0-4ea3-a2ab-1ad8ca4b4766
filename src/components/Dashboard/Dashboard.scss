@import "../../assets/scss/abstracts/variables";
@import "../../assets/scss/abstracts/mixins";

.dashboard {
  position: relative;
  width: 80%;
  max-width: 50rem;
  margin: 0 auto 3rem;
  overflow: hidden;
  transition: 0.5s ease-in-out 0s;
  background-color: $color-white;
  box-shadow: -0.2rem 0.2rem 0.2rem -0.1rem rgba($color-black, 0.15);

  @include media("md") {
    width: 100%;
    max-width: inherit;
  }
}
