import React from 'react';
import PropTypes from 'prop-types';
import { iconMap } from '../../assets/images/icons/icons-list.js';
import './UIIcon.scss';

const UIIcon = ({ name, className = '' }) => {
  const iconId = iconMap[name];

  if (!iconId) {
    console.warn(`Icon "${name}" not found in iconMap`);
    return null;
  }

  return (
    <i className={`icon ${iconId} ${className}`}>
      <svg className="icon__svg">
        <use className="icon__use" xlinkHref={`#${iconId}`} />
      </svg>
    </i>
  );
};

UIIcon.propTypes = {
  name: PropTypes.oneOf(Object.keys(iconMap)).isRequired,
  className: PropTypes.string,
};

export default UIIcon;
