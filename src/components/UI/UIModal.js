import React from 'react';
import PropTypes from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/free-solid-svg-icons';
import UIButton from './UIButton';
import './UIModal.scss';

const UIModal = ({ 
  isOpen, 
  onClose, 
  children, 
  trigger,
  className = '' 
}) => {
  return (
    <div className={`modal ${isOpen ? 'is-open' : ''} ${className}`}>
      {trigger}
      <div className="modal__overlay">
        <div className="modal__box">
          <UIButton
            className="modal__button-close button--line-black button--small"
            onClick={onClose}
          >
            <span className="button__icon">
              <i className="icon">
                <FontAwesomeIcon icon={faTimes} />
              </i>
            </span>
          </UIButton>
          {children}
        </div>
      </div>
    </div>
  );
};

UIModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  children: PropTypes.node.isRequired,
  trigger: PropTypes.node,
  className: PropTypes.string,
};

export default UIModal;
