import React from 'react';
import PropTypes from 'prop-types';
import './UIButton.scss';

const UIButton = ({ 
  href, 
  to, 
  value, 
  className = '', 
  onClick, 
  children,
  ...props 
}) => {
  const handleClick = (event) => {
    if (onClick) {
      onClick(event);
    }
  };

  const getTag = () => {
    if (href) {
      return 'a';
    } else if (value) {
      return 'input';
    } else if (to) {
      // For router links, we'll use a regular button for now
      // In a real app, you'd use React Router's Link component
      return 'button';
    }
    return 'button';
  };

  const Tag = getTag();
  const buttonProps = {
    className: `button ${className}`,
    onClick: handleClick,
    ...props,
  };

  if (href) {
    buttonProps.href = href;
  }
  if (value) {
    buttonProps.value = value;
  }

  return (
    <Tag {...buttonProps}>
      {children}
    </Tag>
  );
};

UIButton.propTypes = {
  href: PropTypes.string,
  to: PropTypes.string,
  value: PropTypes.string,
  className: PropTypes.string,
  onClick: PropTypes.func,
  children: PropTypes.node,
};

export default UIButton;
