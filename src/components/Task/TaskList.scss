@import "../../assets/scss/abstracts/variables";

.task-list {
  display: flex;
  position: relative;
  flex-direction: column;
  list-style: none;

  &__wrapper {
    height: calc(5.6rem * 3);
    margin: 2rem;
    overflow-y: scroll;
  }

  &__item {
    padding: 0.5rem;

    /* Animation for the item transition */
    transition: all 0.3s ease-in-out;

    &:hover {
      transform: scale(1.02);
    }
  }

  &__message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: $color-light;
    font-size: 2rem;
    line-height: 100%;
    text-align: center;

    > * {
      &:not(:last-child) {
        margin-bottom: 1rem;
      }
    }

    .icon {
      width: 15rem;
      height: 10rem;
    }
  }
}
