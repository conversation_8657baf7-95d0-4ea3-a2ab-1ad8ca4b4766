import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { selectTaskList } from '../../store/slices/tasksSlice';
import { checkTaskListLocalStorage, createTaskListLocalStorage } from '../../store/thunks/tasksThunks';
import DashboardInfo from './DashboardInfo';
import DashboardContent from './DashboardContent';
import './Dashboard.scss';

const Dashboard = () => {
  const dispatch = useDispatch();
  const taskList = useSelector(selectTaskList);

  useEffect(() => {
    dispatch(checkTaskListLocalStorage());
  }, [dispatch]);

  useEffect(() => {
    dispatch(createTaskListLocalStorage());
  }, [taskList, dispatch]);

  return (
    <div className="dashboard">
      <DashboardInfo />
      <DashboardContent />
    </div>
  );
};

export default Dashboard;
