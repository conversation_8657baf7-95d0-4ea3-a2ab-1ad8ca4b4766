import { createSlice } from '@reduxjs/toolkit';
import tasksData from '../../assets/data/tasks.json';

const initialState = {
  list: tasksData,
};

const tasksSlice = createSlice({
  name: 'tasks',
  initialState,
  reducers: {
    addTask: (state, action) => {
      const taskNew = {
        id: action.payload.id,
        name: action.payload.name,
        status: {
          done: false,
          show: false,
        },
      };
      state.list.push(taskNew);
    },
    updateTask: (state, action) => {
      const taskIndex = state.list.findIndex(item => item.id === action.payload.id);
      if (taskIndex !== -1) {
        state.list[taskIndex] = action.payload;
      }
    },
    removeTask: (state, action) => {
      state.list = state.list.filter(item => item.id !== action.payload);
    },
    removeTaskList: (state) => {
      state.list = [];
    },
    removeTaskListDone: (state) => {
      state.list = state.list.filter(item => !item.status.done);
    },
    updateTaskListFromLocalStorage: (state, action) => {
      state.list = action.payload;
    },
  },
});

export const {
  addTask,
  updateTask,
  removeTask,
  removeTaskList,
  removeTaskListDone,
  updateTaskListFromLocalStorage,
} = tasksSlice.actions;

// Selectors
export const selectTaskList = (state) => state.tasks.list;
export const selectTotalTaskList = (state) => state.tasks.list.length;
export const selectTotalTaskListDone = (state) => 
  state.tasks.list.filter(item => item.status.done).length;
export const selectTaskLast = (state) => 
  state.tasks.list[state.tasks.list.length - 1];

export default tasksSlice.reducer;
