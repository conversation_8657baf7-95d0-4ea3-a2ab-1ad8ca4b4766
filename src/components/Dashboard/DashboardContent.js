import React, { useRef } from 'react';
import TaskList from '../Task/TaskList';
import TaskNew from '../Task/TaskNew';

const DashboardContent = () => {
  const taskListRef = useRef(null);

  const scrollToBottom = () => {
    // TaskList component handles its own scrolling
    // This is kept for compatibility but not needed in React version
  };

  return (
    <div className="dashboard-content">
      <TaskList ref={taskListRef} />
      <TaskNew onAddTask={scrollToBottom} />
    </div>
  );
};

export default DashboardContent;
