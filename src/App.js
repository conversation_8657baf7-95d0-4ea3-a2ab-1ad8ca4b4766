import React from 'react';
import { useSelector } from 'react-redux';
import PageTitle from './components/Page/PageTitle';
import Dashboard from './components/Dashboard/Dashboard';
import UIMessage from './components/UI/UIMessage';
import './App.scss';

function App() {
  const pageTitle = useSelector(state => state.app.pageTitle);

  return (
    <div className="app">
      <PageTitle title={pageTitle} />
      <Dashboard />
      <UIMessage>
        <p>This application uses the browser's Local Storage to store data.</p>
      </UIMessage>
    </div>
  );
}

export default App;
