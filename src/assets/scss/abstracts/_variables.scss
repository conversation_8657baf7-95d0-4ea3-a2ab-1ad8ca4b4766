// ABSTRACTS - VARIABLES
// =================================================

// COLORS
// ----------------------------------------------------------------------
$color-1: #9cedff;
$color-2: #3e82c3;
$color-3: #012e59;

// GRAY SCALE
// ----------------------------------------------------------------------
$color-white: #ffffff;
$color-ghost: #f2f2f2;
$color-light: #dcdfe3;
$color-silver: #c3bbb6;
$color-gray: #999999;
$color-dark: #353535;
$color-black: #000000;

// REACT (updated from Vue)
// ----------------------------------------------------------------------
$color-react-1: #61dafb;
$color-react-2: #282c34;

// BRAND
// ----------------------------------------------------------------------
$color-brand-1: $color-react-1;
$color-brand-2: $color-react-2;
$color-brand-3: $color-2;
$color-brand-4: $color-3;

// STATUS
// ----------------------------------------------------------------------
$color-error: #f65050;
$color-success: $color-react-1;

// FONTS
// ----------------------------------------------------------------------
$font-brand-1: "Trebuchet MS", sans-serif;
$font-brand-2: "Roboto", sans-serif;

// BREAKPOINTS
// ----------------------------------------------------------------------
$breakpoints: (
  xs: 480px,    // mobile portrait
  sm: 576px,    // mobile landscape
  md: 768px,    // tablet portrait
  lg: 992px,    // tablet portrait
  xl: 1024px,   // tablet landscape
  xxl: 1280px,  // laptop
  xxxl: 1600px, // desktop
) !default;
