import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { useDispatch } from 'react-redux';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCheckCircle, faEdit, faTrash } from '@fortawesome/free-solid-svg-icons';
import { removeTask, updateTask } from '../../store/slices/tasksSlice';
import UIButton from '../UI/UIButton';
import UIModal from '../UI/UIModal';
import TaskEdit from './TaskEdit';
import './TaskPreview.scss';

const TaskPreview = ({ taskItem }) => {
  const dispatch = useDispatch();
  const [isModalOpen, setIsModalOpen] = useState(false);

  const changeTaskDone = () => {
    const updatedTask = {
      ...taskItem,
      status: {
        ...taskItem.status,
        done: !taskItem.status.done,
      },
    };
    dispatch(updateTask(updatedTask));
  };

  const showTask = () => {
    setIsModalOpen(true);
  };

  const hideTask = () => {
    setIsModalOpen(false);
  };

  const handleRemoveTask = () => {
    dispatch(removeTask(taskItem.id));
  };

  const editTrigger = (
    <UIButton
      className="task-preview__button-edit button--icon"
      onClick={showTask}
    >
      <span className="button__icon">
        <i className="icon">
          <FontAwesomeIcon icon={faEdit} />
        </i>
      </span>
    </UIButton>
  );

  return (
    <div
      className={`task-preview ${taskItem.status.done ? 'is-done' : ''} ${isModalOpen ? 'is-show' : ''}`}
    >
      <UIButton
        className="task-preview__button-done button--icon"
        onClick={changeTaskDone}
      >
        <span className="button__icon">
          <i className="icon">
            <FontAwesomeIcon icon={faCheckCircle} />
          </i>
        </span>
      </UIButton>
      <div className="task-preview__name">
        <p>
          {taskItem.name}
        </p>
      </div>
      <ul className="task-preview__tools">
        <li>
          <UIModal
            isOpen={isModalOpen}
            onClose={hideTask}
            trigger={editTrigger}
            className={isModalOpen ? 'is-open' : ''}
          >
            <TaskEdit
              task={taskItem}
              onEditTask={hideTask}
            />
          </UIModal>
        </li>
        <li>
          <UIButton
            className="task-preview__button-remove button--icon"
            onClick={handleRemoveTask}
          >
            <span className="button__icon">
              <i className="icon">
                <FontAwesomeIcon icon={faTrash} />
              </i>
            </span>
          </UIButton>
        </li>
      </ul>
    </div>
  );
};

TaskPreview.propTypes = {
  taskItem: PropTypes.shape({
    id: PropTypes.number.isRequired,
    name: PropTypes.string.isRequired,
    status: PropTypes.shape({
      done: PropTypes.bool.isRequired,
      show: PropTypes.bool.isRequired,
    }).isRequired,
  }).isRequired,
};

export default TaskPreview;
