@use "sass:color";
@import "../../assets/scss/abstracts/variables";

.task-preview {
  display: flex;
  align-items: center;
  padding: 1rem;
  border-radius: 0.5rem;
  box-shadow: 0 0 0.3rem rgba($color-black, 0.4);
  line-height: 1;
  user-select: none;

  &__name {
    max-width: calc(100% - 10rem);
    transition: all 0.2s ease-in-out;
    font-size: 1.5rem;

    p {
      display: inline-block;
      position: relative;
      width: 100%;
      overflow: hidden;
      line-height: 150%;
      text-overflow: ellipsis;
      white-space: nowrap;

      &:before {
        content: "";
        position: absolute;
        top: 50%;
        left: 0;
        width: 0%;
        height: 0.2rem;
        transition: all 0.2s ease-in-out;
        background-color: $color-black;
      }
    }
  }

  &__button-done {
    margin-right: 1.5rem;
    opacity: 1;
    color: $color-silver;
    font-size: 2.4rem;

    &:hover {
      color: color.mix($color-black, $color-silver, 20%);
    }
  }

  &__tools {
    display: flex;
    margin-left: auto;
    opacity: 0;
    list-style: none;

    > * {
      &:not(:last-child) {
        margin-right: 1.2rem;
      }
    }
  }

  &__button-edit,
  &__button-remove {
    font-size: 2rem;
  }

  &__button-edit {
    color: $color-brand-3;

    &:hover {
      color: color.mix($color-black, $color-brand-3, 20%);
    }
  }

  &__button-remove {
    color: $color-error;

    &:hover {
      color: color.mix($color-black, $color-error, 20%);
    }
  }

  &:hover {
    background-color: $color-ghost;

    .task-preview {
      &__tools {
        opacity: 1;
        cursor: pointer;
      }
    }
  }

  &.is-done {
    .task-preview {
      &__name {
        p {
          &:before {
            width: 100%;
          }
        }
      }

      &__button-done {
        color: $color-success;

        &:hover {
          color: color.mix($color-black, $color-success, 20%);
        }
      }
    }
  }

  &.is-show {
    .task-preview {
      &__tools {
        opacity: 1;
      }
    }
  }
}
