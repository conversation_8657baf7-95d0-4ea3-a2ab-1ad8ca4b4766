@import "../../assets/scss/abstracts/variables";

.task-edit {
  &__title {
    margin-bottom: 2rem;
    font-size: 2.8rem;
    font-weight: bold;
  }

  &__content {
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  &__item {
    display: flex;
    flex-direction: column;

    > * {
      &:not(:last-child) {
        margin-bottom: 1.2rem;
      }
    }
  }

  &__subtitle {
    margin-bottom: 1rem;
    font-size: 1.8rem;
  }

  &__field {
    padding: 1.2rem;
    overflow: hidden;
    border: 0.2rem solid $color-brand-3;
    border-radius: 0.5rem;
    background-color: $color-white;
    font-size: 1.5rem;
  }

  &__button-save {
    align-self: flex-end;
    max-width: 10rem;
    margin-top: 2rem;
  }
}
