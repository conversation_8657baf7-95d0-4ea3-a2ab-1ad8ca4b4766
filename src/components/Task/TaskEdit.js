import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { useDispatch } from 'react-redux';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faEdit } from '@fortawesome/free-solid-svg-icons';
import { updateTask } from '../../store/slices/tasksSlice';
import UIButton from '../UI/UIButton';
import './TaskEdit.scss';

const TaskEdit = ({ task, onEditTask }) => {
  const dispatch = useDispatch();
  const [taskCopy, setTaskCopy] = useState({ ...task });

  useEffect(() => {
    setTaskCopy({ ...task });
  }, [task]);

  const handleInputChange = (e) => {
    setTaskCopy({
      ...taskCopy,
      name: e.target.value,
    });
  };

  const handleSave = () => {
    dispatch(updateTask(taskCopy));
    if (onEditTask) {
      onEditTask();
    }
  };

  return (
    <section className="task-edit">
      <h3 className="task-edit__title">
        Edit task
      </h3>
      <div className="task-edit__content">
        <article className="task-edit__item">
          <label className="task-edit__subtitle">
            Task name
          </label>
          <input
            value={taskCopy.name}
            onChange={handleInputChange}
            className="task-edit__field"
            type="text"
          />
        </article>

        <UIButton
          className="task-edit__button-save button--bg-color-3"
          onClick={handleSave}
        >
          <span className="button__icon">
            <i className="icon">
              <FontAwesomeIcon icon={faEdit} />
            </i>
          </span>
          <span>Save</span>
        </UIButton>
      </div>
    </section>
  );
};

TaskEdit.propTypes = {
  task: PropTypes.shape({
    id: PropTypes.number.isRequired,
    name: PropTypes.string.isRequired,
    status: PropTypes.shape({
      done: PropTypes.bool.isRequired,
      show: PropTypes.bool.isRequired,
    }).isRequired,
  }).isRequired,
  onEditTask: PropTypes.func,
};

export default TaskEdit;
